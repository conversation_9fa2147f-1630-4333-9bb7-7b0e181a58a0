/* Modern Interactive Library Management Dashboard Styles */

/* Dark Mode Support */
[data-theme="dark"] .nav-link {
  color: #9ca3af;
}

[data-theme="dark"] .nav-link:hover {
  color: #f9fafb;
  background-color: #374151;
}

[data-theme="dark"] .nav-link.active {
  color: #60a5fa;
  background-color: #1e3a8a;
  border-right: 2px solid #60a5fa;
}

/* Navigation Styles */
.nav-link {
  color: #6b7280;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: #111827;
  background-color: #f3f4f6;
  transform: translateX(4px);
}

.nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
  border-right: 2px solid #2563eb;
}

/* Logout Button Animation */
.logout-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Widget Animations */
.widget-card {
  transition: all 0.3s ease;
}

.widget-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
}

/* Weather Widget Animations */
#weather-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Stats Counter Animation */
.stat-counter {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* News Item Hover Effects */
.news-item:hover {
  transform: translateX(8px);
}

/* Mobile-only responsive design */
@media (max-width: 1023px) {
  /* Hide the mobile menu button on desktop */
  .lg\:hidden {
    display: block;
  }

  /* Make sidebar mobile-friendly */
  aside.w-64 {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  /* Show sidebar when mobile menu is open */
  aside.w-64[style*="translateX(0)"] {
    transform: translateX(0) !important;
  }

  /* Hide right sidebar on mobile */
  aside.w-80 {
    display: none;
  }

  /* Adjust main content padding for mobile */
  main {
    padding: 1rem !important;
  }

  /* Ensure header content doesn't overflow */
  header {
    padding-left: 4rem !important; /* Account for mobile menu button */
    padding-right: 1rem !important;
  }
}

/* Desktop - keep everything as original */
@media (min-width: 1024px) {
  /* Hide mobile menu button on desktop */
  .lg\:hidden {
    display: none;
  }

  /* Ensure sidebar is visible on desktop */
  aside.w-64 {
    position: relative;
    transform: translateX(0) !important;
  }

  /* Show right sidebar on desktop */
  aside.w-80 {
    display: block;
  }

  /* Hide mobile widgets on desktop */
  .lg\:hidden {
    display: none !important;
  }
}

/* Notification Badge Animation */
.notification-badge {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Smooth Scrolling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark Mode Scrollbar */
[data-theme="dark"] .overflow-y-auto {
  scrollbar-color: #4b5563 #1f2937;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-track {
  background: #1f2937;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #4b5563;
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Dark Mode Toggle Animation */
.dark-mode-toggle {
  transition: transform 0.2s ease;
}

.dark-mode-toggle:hover {
  transform: scale(1.1);
}

/* Mobile Menu Improvements */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
  transition: opacity 0.3s ease;
}

/* Card Hover Effects */
.widget-card:hover {
  transform: translateY(-2px);
}

/* Responsive Text Scaling */
@media (max-width: 640px) {
  .text-3xl {
    font-size: 1.875rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.125rem;
  }
}

/* Enhanced Focus States */
.nav-link:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

[data-theme="dark"] .nav-link:focus,
[data-theme="dark"] button:focus {
  outline-color: #60a5fa;
}

/* Loading States */
.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Improved Transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Mobile Header Improvements */
@media (max-width: 1023px) {
  /* Ensure header text doesn't get cut off */
  .header-title {
    font-size: 1.25rem !important;
    line-height: 1.75rem !important;
  }

  /* Mobile button spacing */
  .header-buttons {
    gap: 0.5rem !important;
  }

  /* Ensure mobile widgets are properly spaced */
  .mobile-widgets {
    margin-top: 2rem;
  }
}

/* Ensure proper text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}